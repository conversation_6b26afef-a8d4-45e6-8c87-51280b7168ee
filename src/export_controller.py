"""Extension interaction controller for automating Patreon exports."""

import logging
from typing import Optional, Dict, Any
from pathlib import Path
from playwright.sync_api import Page, TimeoutError as PlaywrightTimeoutError


class ExportController:
    """Controls the Patreon export extension to automate PDF exports."""
    
    def __init__(self, page: Page, config: 'Config', logger: logging.Logger = None):
        """Initialize the export controller.
        
        Args:
            page: Playwright page instance
            config: Configuration object
            logger: Logger instance
        """
        self.page = page
        self.config = config
        self.logger = logger or logging.getLogger(__name__)
        self.download_dir = Path(config.output_directory)
        
        # Ensure download directory exists
        self.download_dir.mkdir(parents=True, exist_ok=True)
    
    def export_post(self, post_url: str, output_subdir: str = "") -> Dict[str, Any]:
        """Export a single post as PDF using the extension.
        
        Args:
            post_url: URL of the post to export
            output_subdir: Subdirectory for this export
            
        Returns:
            Dictionary with export result information
        """
        self.logger.info(f"Starting export of: {post_url}")
        
        try:
            # Set up output directory
            if output_subdir:
                full_output_dir = self.download_dir / output_subdir
                full_output_dir.mkdir(parents=True, exist_ok=True)
            else:
                full_output_dir = self.download_dir
            
            # Navigate to the post
            self.logger.debug(f"Navigating to post: {post_url}")
            self.page.goto(post_url, wait_until='networkidle', timeout=60000)
            
            # Wait for page to fully load and extension to initialize
            self.page.wait_for_timeout(5000)
            
            # Check if we're actually on a valid post page
            if not self._is_valid_post_page():
                raise Exception("Not a valid post page or post may be private/restricted")
            
            # Trigger the export and wait for download
            downloaded_file = self._trigger_export_and_download(full_output_dir)
            
            if downloaded_file:
                self.logger.info(f"Export successful: {downloaded_file}")
                return {
                    'success': True,
                    'file_path': str(downloaded_file),
                    'file_name': downloaded_file.name,
                    'file_size': downloaded_file.stat().st_size,
                    'post_url': post_url
                }
            else:
                raise Exception("Export completed but no file was downloaded")
                
        except Exception as e:
            self.logger.error(f"Export failed for {post_url}: {e}")
            return {
                'success': False,
                'error': str(e),
                'post_url': post_url
            }
    
    def _is_valid_post_page(self) -> bool:
        """Check if current page is a valid Patreon post page."""
        try:
            # Check URL pattern
            current_url = self.page.url
            if '/posts/' not in current_url:
                return False
            
            # Check for post-specific elements
            post_indicators = [
                'div[data-tag="post-card"]',
                'span[data-tag="post-title"]',
                'article[data-tag="post-card"]'
            ]
            
            for selector in post_indicators:
                if self.page.query_selector(selector):
                    return True
            
            # Check for the extension's button
            try:
                self.page.wait_for_selector('#patreon-exporter-button', timeout=5000)
                button = self.page.query_selector('#patreon-exporter-button')
                if button and not button.is_disabled():
                    return True
            except PlaywrightTimeoutError:
                pass
            
            return False
            
        except Exception as e:
            self.logger.debug(f"Error checking post page validity: {e}")
            return False
    
    def _trigger_export_and_download(self, output_dir: Path) -> Optional[Path]:
        """Trigger export and handle download using Chrome Downloads API monitoring."""

        try:
            # Find the export button
            floating_button = self.page.query_selector('#patreon-exporter-button')
            if not floating_button or not floating_button.is_visible() or floating_button.is_disabled():
                raise Exception("Export button not found or not clickable")

            # Ensure button is in view and page is stable
            floating_button.scroll_into_view_if_needed()
            self.page.wait_for_timeout(1000)

            # Log page info
            page_title = self.page.title()
            self.logger.debug(f"Page title before export: {page_title}")

            # Generate a proper filename based on the page title
            suggested_filename = self._generate_filename_from_page()

            # Create target path
            target_path = output_dir / suggested_filename

            # Handle duplicates
            counter = 1
            while target_path.exists():
                stem = Path(suggested_filename).stem
                suffix = Path(suggested_filename).suffix
                target_path = output_dir / f"{stem} ({counter}){suffix}"
                counter += 1

            # Use Chrome Downloads API monitoring approach
            try:
                return self._monitor_chrome_downloads(floating_button, target_path)
            except Exception as download_error:
                self.logger.error(f"Chrome Downloads API monitoring failed: {download_error}")
                # If download failed, fall back to the old method as a backup
                self.logger.info("Attempting fallback download detection...")
                return self._fallback_download_detection(target_path)

        except Exception as e:
            self.logger.error(f"Export and download failed: {e}")
            return None

    def _monitor_chrome_downloads(self, floating_button, target_path: Path) -> Optional[Path]:
        """Monitor Chrome downloads directory for new files."""
        import time

        try:
            # Get Chrome's default downloads directory
            downloads_dirs = self._get_chrome_downloads_directories()

            if not downloads_dirs:
                raise Exception("Could not determine Chrome downloads directory")

            self.logger.debug(f"Monitoring downloads directories: {downloads_dirs}")

            # Get initial state of downloads directories
            initial_files = set()
            for downloads_dir in downloads_dirs:
                if downloads_dir.exists():
                    try:
                        for file_path in downloads_dir.glob("*.pdf"):
                            if file_path.is_file() and file_path.stat().st_size > 100000:  # > 100KB
                                initial_files.add(str(file_path))
                    except (PermissionError, OSError):
                        continue

            self.logger.debug(f"Initial PDF files in downloads: {len(initial_files)}")

            # Trigger the download by clicking the button
            self.logger.debug("Clicking export button...")
            floating_button.click()

            # Wait for download completion
            self.logger.info("Waiting for Chrome download completion...")
            timeout = 120  # 120 seconds as requested
            start_time = time.time()
            last_button_text = ""
            check_interval = 2  # Check every 2 seconds
            last_check_time = 0

            while time.time() - start_time < timeout:
                current_time = time.time()

                # Check export status
                try:
                    button_text = floating_button.text_content()
                    if button_text and button_text != last_button_text:
                        self.logger.info(f"Export status: {button_text}")
                        last_button_text = button_text
                except Exception:
                    pass

                # Check for new files every few seconds
                if current_time - last_check_time >= check_interval:
                    last_check_time = current_time

                    # Look for new PDF files in downloads directories
                    for downloads_dir in downloads_dirs:
                        if not downloads_dir.exists():
                            continue

                        try:
                            for file_path in downloads_dir.glob("*.pdf"):
                                if (file_path.is_file() and
                                    str(file_path) not in initial_files and
                                    file_path.stat().st_size > 100000 and  # > 100KB
                                    file_path.stat().st_mtime > start_time - 5):  # Created recently

                                    self.logger.info(f"Found new download: {file_path.name} ({file_path.stat().st_size / (1024*1024):.1f}MB)")

                                    # Wait a moment for file to be fully written
                                    time.sleep(1)

                                    # Move/copy the file to our target location
                                    try:
                                        import shutil
                                        shutil.copy2(file_path, target_path)

                                        # Verify the copy was successful
                                        if target_path.exists() and target_path.stat().st_size > 0:
                                            self.logger.info(f"Successfully moved download: {target_path.name} ({target_path.stat().st_size / (1024*1024):.1f}MB)")

                                            # Clean up original download
                                            try:
                                                file_path.unlink()
                                                self.logger.debug(f"Cleaned up original download: {file_path}")
                                            except Exception as e:
                                                self.logger.warning(f"Could not clean up original download: {e}")

                                            return target_path
                                        else:
                                            self.logger.error("File copy failed or resulted in empty file")

                                    except Exception as e:
                                        self.logger.error(f"Failed to move download file: {e}")

                        except (PermissionError, OSError) as e:
                            self.logger.debug(f"Error accessing downloads directory {downloads_dir}: {e}")
                            continue

                time.sleep(1)  # Check every second

            # If we get here, it's a timeout
            raise Exception("Download timeout - no new PDF found in downloads directory within 120 seconds")

        except Exception as e:
            self.logger.error(f"Chrome downloads monitoring failed: {e}")
            raise

    def _get_chrome_downloads_directories(self):
        """Get possible Chrome downloads directories."""
        from pathlib import Path

        possible_dirs = []

        # Default Downloads folder
        home = Path.home()
        downloads_dir = home / "Downloads"
        if downloads_dir.exists():
            possible_dirs.append(downloads_dir)

        # Try to get Chrome's configured downloads directory via JavaScript
        try:
            # This might not work due to security restrictions, but worth trying
            downloads_path_js = """
            new Promise((resolve) => {
                if (typeof chrome !== 'undefined' && chrome.downloads) {
                    chrome.downloads.search({limit: 1}, (downloads) => {
                        if (downloads && downloads.length > 0) {
                            const path = downloads[0].filename;
                            const dir = path.substring(0, path.lastIndexOf('/'));
                            resolve(dir);
                        } else {
                            resolve(null);
                        }
                    });
                } else {
                    resolve(null);
                }
            });
            """

            result = self.page.evaluate(downloads_path_js)
            if result:
                chrome_downloads_dir = Path(result)
                if chrome_downloads_dir.exists() and chrome_downloads_dir not in possible_dirs:
                    possible_dirs.append(chrome_downloads_dir)

        except Exception as e:
            self.logger.debug(f"Could not get Chrome downloads directory via JS: {e}")

        return possible_dirs

    def _fallback_download_detection(self, target_path: Path) -> Optional[Path]:
        """Fallback method using the old playwright artifacts monitoring approach."""
        import tempfile
        import time

        try:
            self.logger.info("Using fallback download detection method...")

            # Monitor playwright artifacts directory for new files
            temp_base = Path(tempfile.gettempdir())
            playwright_dirs = list(temp_base.glob("playwright-artifacts-*"))

            self.logger.debug(f"Monitoring {len(playwright_dirs)} playwright artifacts directories")

            # Look for recently created files
            timeout = 60  # Increased timeout for fallback
            start_time = time.time()
            check_interval = 2  # Check every 2 seconds
            last_check_time = 0

            while time.time() - start_time < timeout:
                current_time = time.time()

                # Check for new files every few seconds
                if current_time - last_check_time >= check_interval:
                    last_check_time = current_time

                    # Look for recently created files
                    recent_files = []
                    for temp_dir in temp_base.glob("playwright-artifacts-*"):
                        try:
                            for file_path in temp_dir.rglob("*"):
                                if file_path.is_file():
                                    if file_path.stat().st_size > 100000:
                                        if (file_path.suffix.lower() == '.pdf' or self._is_pdf_file(file_path)):
                                            # Check if file was created after we started the export
                                            if file_path.stat().st_mtime > start_time - 10:
                                                recent_files.append(file_path)
                        except (PermissionError, OSError):
                            continue

                    if recent_files:
                        # Found recently created PDF file(s)
                        newest_file = max(recent_files, key=lambda f: f.stat().st_mtime)
                        age_seconds = current_time - newest_file.stat().st_mtime
                        self.logger.info(f"Fallback download detected: {newest_file.name} ({newest_file.stat().st_size / (1024*1024):.1f}MB, {age_seconds:.1f}s old)")

                        # Copy the file to our target path
                        import shutil
                        shutil.copy2(newest_file, target_path)

                        # Clean up the temp file after successful copy
                        try:
                            newest_file.unlink()
                            self.logger.info(f"Cleaned up temp file: {newest_file.name}")
                        except Exception as e:
                            self.logger.error(f"Could not remove temp file {newest_file}: {e}")

                        if target_path.exists() and target_path.stat().st_size > 0:
                            self.logger.info(f"Fallback method successfully saved: {target_path.name}")
                            return target_path

                time.sleep(1)  # Check every second

            self.logger.error("Fallback download timeout - no file found")
            return None

        except Exception as e:
            self.logger.error(f"Fallback download detection failed: {e}")
            return None
    
    def _generate_filename_from_page(self) -> str:
        """Generate a proper filename from page title (without date - will be added later)."""
        try:
            # Get page title
            title = self.page.title()
            
            # Clean up title for filename - remove problematic symbols but keep %
            import re
            # Remove or replace problematic characters for cross-platform compatibility
            # Keep percentage (%) as the only exception symbol
            clean_title = re.sub(r'[<>:"/\\|?*]', '_', title)  # Replace filesystem reserved chars
            clean_title = re.sub(r'[!@#$^&()+=\[\]{};\'",~`]', '_', clean_title)  # Replace other symbols
            clean_title = re.sub(r'[｜🔥🏠丨]', '_', clean_title)  # Replace pipes and emojis
            clean_title = re.sub(r'\s+', '_', clean_title)  # Replace multiple spaces with single underscore
            clean_title = re.sub(r'_+', '_', clean_title)  # Replace multiple underscores with single
            clean_title = clean_title.strip('_')  # Remove leading/trailing underscores
            clean_title = clean_title[:100]  # Limit length
            
            # Ensure we have a valid filename
            if not clean_title:
                clean_title = "patreon_post"
            
            # Don't add current date here - the post date will be added in the rename step
            return f"{clean_title}.pdf"
            
        except Exception:
            # Fallback filename
            from datetime import datetime
            return f"patreon_export_{datetime.now().strftime('%H%M%S')}.pdf"
    
    def _is_pdf_file(self, file_path: Path) -> bool:
        """Check if a file is a PDF by reading its header."""
        try:
            with open(file_path, 'rb') as f:
                header = f.read(4)
                return header == b'%PDF'
        except Exception:
            return False
    
